#!/usr/bin/env python3
"""
Test script to verify the food analysis error handling
"""

import json
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_error_detection():
    """Test the error detection patterns"""
    
    # Import the patterns from the daily_food module
    from front.pages.daily_food import display_analysis_results
    
    # Test cases with different error messages
    test_cases = [
        {
            "name": "No food detected",
            "result": {"error": "No food image detected. The provided image appears to be a selfie of a person, not a food item."},
            "should_be_no_food": True
        },
        {
            "name": "Not a food item",
            "result": {"error": "This appears to be a landscape photo, not a food item."},
            "should_be_no_food": True
        },
        {
            "name": "General error",
            "result": {"error": "API connection failed"},
            "should_be_no_food": False
        },
        {
            "name": "Success case",
            "result": {
                "success": True,
                "food_name": "Apple",
                "heart_health_score": 8,
                "nutrition": {"calories": 95}
            },
            "should_be_no_food": False
        }
    ]
    
    # Test the error patterns
    no_food_patterns = [
        'no food',
        'not a food',
        'appears to be a selfie',
        'not food item',
        'no food image detected',
        'food not detected',
        'not food-related',
        'does not contain food'
    ]
    
    print("🧪 Testing food analysis error detection...")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n📋 Test: {test_case['name']}")
        result = test_case['result']
        
        if 'error' in result:
            error_message = result.get('error', '')
            error_lower = error_message.lower()
            is_no_food_error = any(pattern in error_lower for pattern in no_food_patterns)
            
            print(f"   Error message: {error_message}")
            print(f"   Detected as 'no food': {is_no_food_error}")
            print(f"   Expected 'no food': {test_case['should_be_no_food']}")
            
            if is_no_food_error == test_case['should_be_no_food']:
                print("   ✅ PASS")
            else:
                print("   ❌ FAIL")
        else:
            print(f"   No error field - success case")
            print("   ✅ PASS")
    
    print("\n" + "=" * 50)
    print("🎉 Error detection test completed!")

if __name__ == "__main__":
    test_error_detection()
