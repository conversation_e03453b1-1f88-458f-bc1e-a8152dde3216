import streamlit as st
import warnings
import sys
import os

# Suppress deprecation warnings (optional - uncomment if needed)
# warnings.filterwarnings("ignore", category=DeprecationWarning)

from pages import daily_food, login, tasks, assistant_voice

# Import enhanced UI components
sys.path.append(os.path.dirname(__file__))
from components.ui_theme import apply_theme, create_header, create_metric_grid, CVDTheme

# Constants
PAGE_HOME = "Home"
PAGE_DAILY_FOOD = "Daily Food"
PAGE_TASK = "Task"
PAGE_ASSISTANT = "Assistant"

PAGES = [PAGE_HOME, PAGE_DAILY_FOOD, PAGE_TASK, PAGE_ASSISTANT]

# Configure page
st.set_page_config(
    page_title="MIT CVD Health App",
    page_icon="❤️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'user' not in st.session_state:
    st.session_state.user = None

# Check if user is logged in
if not st.session_state.logged_in:
    # Show login page if not logged in
    login.main()
    # Stop execution here to prevent showing the main app
    st.stop()

# Show main app with sidebar if logged in
st.sidebar.write(f"👋 Hello, **{st.session_state.user['name']}**")

if st.sidebar.button("🚪 Logout"):
    st.session_state.logged_in = False
    st.session_state.user = None
    if 'redirect_to' in st.session_state:
        del st.session_state.redirect_to
    if 'current_page' in st.session_state:
        del st.session_state.current_page
    st.rerun()

st.sidebar.markdown("---")

# Centralized page navigation state
if 'current_page' not in st.session_state:
    st.session_state.current_page = PAGE_HOME

def set_page(page):
    st.session_state.current_page = page

# Handle redirects from login or quick actions
if 'redirect_to' in st.session_state:
    st.session_state.current_page = st.session_state.redirect_to
    del st.session_state.redirect_to

side_bar = st.sidebar.selectbox(
    "Select a page",
    options=PAGES,
    index=PAGES.index(st.session_state.current_page) if st.session_state.current_page in PAGES else 0,
    on_change=lambda: set_page(st.session_state['new_page']),
    key='new_page'
)

# Update current page based on sidebar selection
st.session_state.current_page = side_bar

# Main router based on current page
if st.session_state.current_page == PAGE_HOME:
        # Apply enhanced theme
        apply_theme()

        # Create styled header
        create_header(
            "MIT CVD Health Dashboard",
            f"Welcome back, {st.session_state.user['name']}! Track your cardiovascular health journey.",
            "🏠"
        )
        
        # Enhanced Patient Information Section
        st.markdown('<div class="cvd-card">', unsafe_allow_html=True)
        st.markdown("### 📋 **Personal Profile**")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### 👤 **Personal Information**")
            user_info = [
                f"**Name:** {st.session_state.user['name']}",
                f"**Email:** {st.session_state.user['email']}",
                f"**Username:** {st.session_state.user['username']}",
                f"**Member since:** {st.session_state.user['created_at'][:10]}"
            ]
            if st.session_state.user.get('age'):
                user_info.append(f"**Age:** {st.session_state.user['age']} years")

            for info in user_info:
                st.markdown(info)

        with col2:
            st.markdown("#### 🏃‍♂️ **Lifestyle Factors**")
            profile = st.session_state.user.get('profile', {})
            lifestyle = profile.get('lifestyle', {})

            smoking_status = "❌ Non-smoker" if not lifestyle.get('smoking', False) else "🚬 Smoker"
            lifestyle_info = [
                f"**Smoking:** {smoking_status}",
                f"**Exercise/week:** {lifestyle.get('exercise_frequency', 0)}x",
                f"**Stress level:** {lifestyle.get('stress_level', 'N/A')}/10",
                f"**Sleep hours:** {lifestyle.get('sleep_hours', 'N/A')}h"
            ]

            for info in lifestyle_info:
                st.markdown(info)

        with col3:
            st.markdown("#### 🧬 **Family History**")
            family_history = profile.get('family_history', {})

            heart_attack = "✅ Yes" if family_history.get('heart_attack', False) else "❌ No"
            high_bp = "✅ Yes" if family_history.get('high_blood_pressure', False) else "❌ No"
            family_info = [
                f"**Family heart attack:** {heart_attack}",
                f"**Family hypertension:** {high_bp}"
            ]

            for info in family_info:
                st.markdown(info)

        st.markdown('</div>', unsafe_allow_html=True)
        
        # Enhanced CVD Scores Section
        cvd_scores = st.session_state.user.get('cvd_scores', [])

        if cvd_scores:
            st.markdown('<div class="cvd-card">', unsafe_allow_html=True)
            st.markdown("### ❤️ **Cardiovascular Risk Assessment**")

            # Latest score summary
            latest_score = cvd_scores[-1]
            score_value = latest_score['score']
            risk_level = latest_score['risk_level']

            # Create enhanced metrics
            metrics_data = [
                {
                    "icon": "📊",
                    "label": "Current CVD Score",
                    "value": f"{score_value:.1%}"
                },
                {
                    "icon": "🔴" if risk_level == "High" else "🟡" if risk_level == "Moderate" else "🟢",
                    "label": "Risk Level",
                    "value": risk_level
                },
                {
                    "icon": "📈",
                    "label": "Total Assessments",
                    "value": str(len(cvd_scores))
                },
                {
                    "icon": "📅",
                    "label": "Last Assessment",
                    "value": latest_score['calculated_at'][:10]
                }
            ]

            create_metric_grid(metrics_data, 4)
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Risk factors details
            st.markdown("#### 🔍 **Last Assessment Details**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**⚠️ Risk Factors:**")
                risk_factors = latest_score.get('factors', {}).get('risk_factors', [])
                if risk_factors:
                    for factor in risk_factors:
                        st.write(f"• {factor}")
                else:
                    st.write("✅ No risk factors identified")
            
            with col2:
                st.markdown("**💪 Protective Factors:**")
                protective_factors = latest_score.get('factors', {}).get('protective_factors', [])
                if protective_factors:
                    for factor in protective_factors:
                        st.write(f"• {factor}")
                else:
                    st.write("❌ No protective factors identified")
            
            # Risk breakdown
            factors = latest_score.get('factors', {})
            if factors:
                st.markdown("#### 📊 **Risk Breakdown**")
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    age_risk = factors.get('age_risk', 0)
                    st.metric("👴 Age Risk", f"{age_risk:.2f}")
                
                with col2:
                    family_risk = factors.get('family_risk', 0)
                    st.metric("🧬 Family Risk", f"{family_risk:.2f}")
                
                with col3:
                    lifestyle_risk = factors.get('lifestyle_risk', 0)
                    st.metric("🏃 Lifestyle Risk", f"{lifestyle_risk:.2f}")
                
                with col4:
                    nutrition_risk = factors.get('nutrition_risk', 0)
                    st.metric("🍎 Nutritional Risk", f"{nutrition_risk:.2f}")
            
            # Historical trend
            if len(cvd_scores) > 1:
                st.markdown('<div class="cvd-card">', unsafe_allow_html=True)
                st.markdown("#### 📈 **Historical Trend**")

                try:
                    import pandas as pd

                    # Create dataframe for chart
                    dates = [score['calculated_at'][:10] for score in cvd_scores]
                    scores = [score['score'] for score in cvd_scores]

                    chart_data = pd.DataFrame({
                        'Date': dates,
                        'CVD Score': scores
                    })

                    st.line_chart(chart_data.set_index('Date'))
                except ImportError:
                    st.info("📊 Chart visualization requires pandas. Install with: pip install pandas")

                st.markdown('</div>', unsafe_allow_html=True)
        
        else:
            st.markdown('<div class="cvd-card">', unsafe_allow_html=True)
            st.markdown("### ❤️ **Cardiovascular Risk Assessment**")
            st.info("🔍 You haven't performed any cardiovascular risk assessments yet.")
            st.markdown("💡 **Get started:** Use the app features to perform your first assessment!")
            st.markdown('</div>', unsafe_allow_html=True)

        # Enhanced Quick Actions
        st.markdown('<div class="cvd-card">', unsafe_allow_html=True)
        st.markdown("### 🚀 **Quick Actions**")
        st.markdown("Jump to key features to manage your cardiovascular health")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 10px; margin: 0.5rem 0;">
                <h4 style="color: white; margin: 0;">🍎 Food Analysis</h4>
                <p style="color: white; margin: 0.5rem 0;">Analyze your meals for heart health</p>
            </div>
            """, unsafe_allow_html=True)
            if st.button("Start Food Analysis", use_container_width=True, key="home_food_btn"):
                st.session_state.redirect_to = PAGE_DAILY_FOOD
                st.rerun()

        with col2:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                        border-radius: 10px; margin: 0.5rem 0;">
                <h4 style="color: white; margin: 0;">📋 Health Tasks</h4>
                <p style="color: white; margin: 0.5rem 0;">Track your health goals</p>
            </div>
            """, unsafe_allow_html=True)
            if st.button("View Tasks", use_container_width=True, key="home_task_btn"):
                st.session_state.redirect_to = PAGE_TASK
                st.rerun()

        with col3:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
                        border-radius: 10px; margin: 0.5rem 0;">
                <h4 style="color: white; margin: 0;">🤖 AI Assistant</h4>
                <p style="color: white; margin: 0.5rem 0;">Get personalized health advice</p>
            </div>
            """, unsafe_allow_html=True)
            if st.button("Chat with Assistant", use_container_width=True, key="home_assistant_btn"):
                st.session_state.redirect_to = PAGE_ASSISTANT
                st.rerun()

        st.markdown('</div>', unsafe_allow_html=True)

elif st.session_state.current_page == PAGE_DAILY_FOOD:
    daily_food.main()

elif st.session_state.current_page == PAGE_TASK:
    tasks.main()

elif st.session_state.current_page == PAGE_ASSISTANT:
    assistant_voice.main()
