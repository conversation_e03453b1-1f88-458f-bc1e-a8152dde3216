"""
Reusable UI Components for MIT CVD App Frontend
Provides consistent styling and behavior across the application
"""
import streamlit as st
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
import time

class LoadingState(Enum):
    """Enumeration for different loading states"""
    IDLE = "idle"
    ANALYZING_IMAGE = "analyzing_image"
    SAVING_ANALYSIS = "saving_analysis"
    CALCULATING_CVD = "calculating_cvd"
    UPLOADING = "uploading"

class LoadingMessages:
    """Centralized loading messages for different operations"""
    MESSAGES = {
        LoadingState.ANALYZING_IMAGE: "🔍 Analyzing the food...",
        LoadingState.SAVING_ANALYSIS: "💾 Saving analysis...",
        LoadingState.CALCULATING_CVD: "📊 Calculating CVD score...",
        LoadingState.UPLOADING: "📤 Uploading image..."
    }
    
    @classmethod
    def get_message(cls, state: LoadingState) -> str:
        return cls.MESSAGES.get(state, "⏳ Processing...")

class UIStyles:
    """Centralized UI styling constants"""
    
    # Colors
    PRIMARY_COLOR = "#FF6B6B"
    SECONDARY_COLOR = "#4ECDC4"
    SUCCESS_COLOR = "#45B7D1"
    WARNING_COLOR = "#FFA726"
    ERROR_COLOR = "#EF5350"
    
    # Card styling
    CARD_STYLE = """
    <style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .food-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid #4ECDC4;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }
    .risk-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .success-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    </style>
    """

class StateManager:
    """Manages UI state for loading operations"""
    
    def __init__(self, session_key: str = "ui_state"):
        self.session_key = session_key
        if self.session_key not in st.session_state:
            st.session_state[self.session_key] = {
                'loading_state': LoadingState.IDLE,
                'operation_data': {}
            }
    
    def set_loading_state(self, state: LoadingState, data: Dict[str, Any] = None):
        """Set the current loading state"""
        st.session_state[self.session_key]['loading_state'] = state
        if data:
            st.session_state[self.session_key]['operation_data'] = data
    
    def get_loading_state(self) -> LoadingState:
        """Get the current loading state"""
        return st.session_state[self.session_key]['loading_state']
    
    def is_loading(self) -> bool:
        """Check if any operation is currently loading"""
        return self.get_loading_state() != LoadingState.IDLE
    
    def clear_loading(self):
        """Clear the loading state"""
        self.set_loading_state(LoadingState.IDLE)
        st.session_state[self.session_key]['operation_data'] = {}

def show_loading_spinner(state: LoadingState):
    """Display a loading spinner with appropriate message"""
    message = LoadingMessages.get_message(state)
    return st.spinner(message)

def create_metric_card(title: str, value: str, delta: str = None, help_text: str = None):
    """Create a styled metric card"""
    st.markdown(UIStyles.CARD_STYLE, unsafe_allow_html=True)
    
    col1, col2 = st.columns([3, 1])
    with col1:
        st.metric(
            label=title,
            value=value,
            delta=delta,
            help=help_text
        )

def create_food_info_card(food_name: str, health_score: float, nutrition: Dict[str, Any]):
    """Create a styled food information card"""
    st.markdown(UIStyles.CARD_STYLE, unsafe_allow_html=True)
    
    with st.container():
        st.markdown(f'<div class="food-card">', unsafe_allow_html=True)
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader(f"🍽️ {food_name}")
            
        with col2:
            # Health score with color coding
            score_color = "green" if health_score >= 7 else "orange" if health_score >= 4 else "red"
            st.markdown(f"<h3 style='color: {score_color}'>💚 {health_score}/10</h3>", unsafe_allow_html=True)
        
        # Progress bar for health score
        st.progress(health_score / 10)
        
        st.markdown('</div>', unsafe_allow_html=True)

def create_action_button(label: str, key: str, callback: Callable = None, 
                        disabled: bool = False, button_type: str = "primary"):
    """Create a styled action button with callback"""
    
    button_styles = {
        "primary": "🔵",
        "success": "🟢", 
        "warning": "🟡",
        "danger": "🔴"
    }
    
    icon = button_styles.get(button_type, "🔵")
    
    if st.button(f"{icon} {label}", key=key, disabled=disabled, use_container_width=True):
        if callback:
            return callback()
        return True
    return False

def show_cvd_impact_preview(health_score: float, risk_factors: List[str]):
    """Show CVD impact preview with enhanced styling"""
    st.markdown(UIStyles.CARD_STYLE, unsafe_allow_html=True)
    
    # Determine impact
    if health_score >= 8:
        impact_text = "📈 Positive Impact"
        impact_color = "green"
        impact_desc = "This food may improve your CVD score"
        card_class = "success-card"
    elif health_score >= 6:
        impact_text = "⚖️ Neutral Impact"
        impact_color = "orange"
        impact_desc = "This food has moderate impact on CVD"
        card_class = "food-card"
    else:
        impact_text = "📉 Negative Impact"
        impact_color = "red"
        impact_desc = "This food may worsen your CVD score"
        card_class = "risk-card"
    
    st.markdown(f'<div class="{card_class}">', unsafe_allow_html=True)
    st.markdown("**🎯 Impact on CVD Score:**")
    st.markdown(f"<span style='color: {impact_color}'><b>{impact_text}</b></span>", unsafe_allow_html=True)
    st.caption(impact_desc)
    
    if risk_factors:
        st.warning(f"⚠️ Risk factors identified: {len(risk_factors)}")
    
    st.markdown('</div>', unsafe_allow_html=True)

def create_camera_selector():
    """Create camera selection interface"""
    st.markdown("### 📷 Camera Options")
    
    # Camera selection (simulated - actual implementation would depend on device capabilities)
    camera_options = ["Default Camera", "Front Camera", "Back Camera"]
    selected_camera = st.selectbox(
        "Select Camera",
        camera_options,
        help="Choose which camera to use for capturing food images"
    )
    
    # Camera settings
    with st.expander("📸 Camera Settings"):
        col1, col2 = st.columns(2)
        
        with col1:
            flash_enabled = st.checkbox("🔦 Enable Flash", value=False)
            
        with col2:
            high_quality = st.checkbox("🎯 High Quality", value=True)
    
    return {
        "camera": selected_camera,
        "flash": flash_enabled,
        "quality": "high" if high_quality else "standard"
    }

def show_nutrition_table(nutrition: Dict[str, Any]):
    """Display nutrition information in a formatted table"""
    if not nutrition:
        return
    
    st.markdown("### 📊 Nutritional Information")
    
    # Create organized nutrition display
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("🔥 Calories", f"{nutrition.get('calories', 'N/A')}")
        st.metric("🧈 Saturated Fat", f"{nutrition.get('saturated_fat', 'N/A')} g")
    
    with col2:
        st.metric("🧂 Sodium", f"{nutrition.get('sodium', 'N/A')} mg")
        st.metric("🌾 Fiber", f"{nutrition.get('fiber', 'N/A')} g")
    
    with col3:
        st.metric("🍯 Sugar", f"{nutrition.get('sugar', 'N/A')} g")
        st.metric("🥩 Protein", f"{nutrition.get('protein', 'N/A')} g")

def show_error_with_suggestions(error_message: str, suggestions: List[str] = None):
    """Show error message with helpful suggestions"""
    st.error(f"❌ {error_message}")
    
    if suggestions:
        with st.expander("💡 Helpful Tips"):
            for suggestion in suggestions:
                st.info(f"• {suggestion}")

# Context managers for loading states
class LoadingContext:
    """Context manager for handling loading states"""
    
    def __init__(self, state_manager: StateManager, loading_state: LoadingState):
        self.state_manager = state_manager
        self.loading_state = loading_state
        self.spinner = None
    
    def __enter__(self):
        self.state_manager.set_loading_state(self.loading_state)
        self.spinner = show_loading_spinner(self.loading_state)
        return self.spinner.__enter__()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.state_manager.clear_loading()
        if self.spinner:
            return self.spinner.__exit__(exc_type, exc_val, exc_tb)
