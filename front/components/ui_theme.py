"""
Enhanced UI Theme and Styling System for MIT CVD App
Provides modern, responsive design components and consistent theming
"""
import streamlit as st
from typing import Dict, Any, List, Optional

class CVDTheme:
    """Enhanced theme system for the CVD app"""
    
    # Color palette
    COLORS = {
        'primary': '#FF6B6B',
        'secondary': '#4ECDC4', 
        'success': '#45B7D1',
        'warning': '#FFA726',
        'error': '#EF5350',
        'info': '#42A5F5',
        'light': '#F8F9FA',
        'dark': '#343A40',
        'gradient_1': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'gradient_2': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        'gradient_3': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'gradient_4': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    }
    
    # Typography
    FONTS = {
        'primary': '"Segoe UI", "Roboto", sans-serif',
        'secondary': '"Inter", "Arial", sans-serif',
        'mono': '"Fira Code", "Consolas", monospace'
    }
    
    # Spacing
    SPACING = {
        'xs': '0.25rem',
        'sm': '0.5rem', 
        'md': '1rem',
        'lg': '1.5rem',
        'xl': '2rem',
        'xxl': '3rem'
    }
    
    # Border radius
    RADIUS = {
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        'round': '50%'
    }

    @classmethod
    def get_global_styles(cls) -> str:
        """Get global CSS styles for the application"""
        return f"""
        <style>
        /* Global Styles */
        .main .block-container {{
            padding-top: 2rem;
            padding-bottom: 2rem;
            max-width: 1200px;
        }}
        
        /* Custom Cards */
        .cvd-card {{
            background: white;
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            margin: {cls.SPACING['md']} 0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }}
        
        .cvd-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }}
        
        .metric-card {{
            background: {cls.COLORS['gradient_1']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            color: white;
            margin: {cls.SPACING['sm']} 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-align: center;
        }}
        
        .food-card {{
            background: white;
            padding: {cls.SPACING['xl']};
            border-radius: {cls.RADIUS['xl']};
            border-left: 4px solid {cls.COLORS['secondary']};
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin: {cls.SPACING['lg']} 0;
        }}
        
        .risk-card {{
            background: {cls.COLORS['gradient_2']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            margin: {cls.SPACING['sm']} 0;
            color: #333;
        }}
        
        .success-card {{
            background: {cls.COLORS['gradient_3']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            margin: {cls.SPACING['sm']} 0;
            color: #333;
        }}
        
        .info-card {{
            background: {cls.COLORS['gradient_4']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            margin: {cls.SPACING['sm']} 0;
            color: #333;
        }}
        
        /* Enhanced Buttons */
        .stButton > button {{
            border-radius: {cls.RADIUS['md']};
            border: none;
            padding: {cls.SPACING['sm']} {cls.SPACING['lg']};
            font-weight: 600;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}
        
        .stButton > button:hover {{
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }}
        
        /* Camera Interface */
        .camera-container {{
            background: {cls.COLORS['light']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            border: 2px dashed {cls.COLORS['secondary']};
            text-align: center;
            margin: {cls.SPACING['md']} 0;
        }}
        
        .camera-settings {{
            background: white;
            padding: {cls.SPACING['md']};
            border-radius: {cls.RADIUS['md']};
            border: 1px solid #e9ecef;
            margin: {cls.SPACING['sm']} 0;
        }}
        
        /* Progress Bars */
        .stProgress > div > div > div > div {{
            background: {cls.COLORS['gradient_1']};
            border-radius: {cls.RADIUS['sm']};
        }}
        
        /* Metrics */
        .metric-container {{
            background: white;
            padding: {cls.SPACING['md']};
            border-radius: {cls.RADIUS['md']};
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: {cls.SPACING['sm']};
        }}
        
        /* Alerts */
        .alert-success {{
            background: {cls.COLORS['gradient_3']};
            border: none;
            border-radius: {cls.RADIUS['md']};
            padding: {cls.SPACING['md']};
            color: #333;
        }}
        
        .alert-warning {{
            background: {cls.COLORS['gradient_2']};
            border: none;
            border-radius: {cls.RADIUS['md']};
            padding: {cls.SPACING['md']};
            color: #333;
        }}
        
        .alert-info {{
            background: {cls.COLORS['gradient_4']};
            border: none;
            border-radius: {cls.RADIUS['md']};
            padding: {cls.SPACING['md']};
            color: #333;
        }}
        
        /* Sidebar Enhancements */
        .css-1d391kg {{
            background: {cls.COLORS['light']};
        }}
        
        /* Header Styling */
        .main-header {{
            background: {cls.COLORS['gradient_1']};
            padding: {cls.SPACING['lg']};
            border-radius: {cls.RADIUS['lg']};
            color: white;
            text-align: center;
            margin-bottom: {cls.SPACING['xl']};
        }}
        
        /* Loading Spinner */
        .stSpinner > div {{
            border-top-color: {cls.COLORS['primary']} !important;
        }}
        
        /* Expander Styling */
        .streamlit-expanderHeader {{
            background: {cls.COLORS['light']};
            border-radius: {cls.RADIUS['md']};
            border: 1px solid #e9ecef;
        }}
        
        /* File Uploader */
        .stFileUploader > div {{
            background: {cls.COLORS['light']};
            border: 2px dashed {cls.COLORS['secondary']};
            border-radius: {cls.RADIUS['lg']};
            padding: {cls.SPACING['lg']};
        }}
        
        /* Camera Input */
        .stCameraInput > div {{
            background: {cls.COLORS['light']};
            border: 2px solid {cls.COLORS['secondary']};
            border-radius: {cls.RADIUS['lg']};
            padding: {cls.SPACING['md']};
        }}
        
        /* Responsive Design */
        @media (max-width: 768px) {{
            .main .block-container {{
                padding-left: 1rem;
                padding-right: 1rem;
            }}
            
            .cvd-card, .food-card {{
                padding: {cls.SPACING['md']};
            }}
        }}
        </style>
        """

def apply_theme():
    """Apply the CVD theme to the current page"""
    st.markdown(CVDTheme.get_global_styles(), unsafe_allow_html=True)

def create_header(title: str, subtitle: str = None, icon: str = "❤️"):
    """Create a styled header section"""
    apply_theme()
    
    st.markdown(f"""
    <div class="main-header">
        <h1>{icon} {title}</h1>
        {f'<p style="margin: 0; opacity: 0.9;">{subtitle}</p>' if subtitle else ''}
    </div>
    """, unsafe_allow_html=True)

def create_info_card(title: str, content: str, icon: str = "ℹ️"):
    """Create an information card"""
    st.markdown(f"""
    <div class="info-card">
        <h4>{icon} {title}</h4>
        <p>{content}</p>
    </div>
    """, unsafe_allow_html=True)

def create_metric_grid(metrics: List[Dict[str, Any]], columns: int = 3):
    """Create a grid of metrics with enhanced styling"""
    cols = st.columns(columns)
    
    for i, metric in enumerate(metrics):
        with cols[i % columns]:
            st.markdown(f"""
            <div class="metric-container">
                <h3 style="margin: 0; color: {CVDTheme.COLORS['primary']};">
                    {metric.get('icon', '📊')} {metric.get('value', 'N/A')}
                </h3>
                <p style="margin: 0.5rem 0 0 0; color: #666;">
                    {metric.get('label', 'Metric')}
                </p>
            </div>
            """, unsafe_allow_html=True)

def create_camera_interface_enhanced():
    """Create an enhanced camera interface with better styling"""
    apply_theme()
    
    st.markdown("""
    <div class="camera-container">
        <h3>📷 Food Photo Capture</h3>
        <p>Position your food clearly in the frame for best analysis results</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Camera settings in a styled container
    with st.container():
        st.markdown('<div class="camera-settings">', unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            camera_options = ["📱 Default Camera", "🤳 Front Camera", "📸 Back Camera"]
            selected_camera = st.selectbox(
                "Camera Selection",
                camera_options,
                help="Choose which camera to use for capturing food images"
            )
        
        with col2:
            quality_options = ["🎯 High Quality", "⚡ Standard Quality"]
            quality = st.selectbox("Image Quality", quality_options)
        
        with col3:
            flash_enabled = st.checkbox("🔦 Enable Flash", value=False)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    return {
        "camera": selected_camera,
        "quality": "high" if "High" in quality else "standard",
        "flash": flash_enabled
    }

def show_loading_with_progress(message: str, progress: float = None):
    """Show loading state with optional progress bar"""
    if progress is not None:
        st.progress(progress)
    
    st.markdown(f"""
    <div style="text-align: center; padding: 1rem;">
        <div style="font-size: 1.2em; color: {CVDTheme.COLORS['primary']};">
            {message}
        </div>
    </div>
    """, unsafe_allow_html=True)
