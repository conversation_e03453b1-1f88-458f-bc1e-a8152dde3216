"""
Specialized components for food analysis functionality
"""
import streamlit as st
import pandas as pd
from typing import Dict, Any, List
from .ui_components import (
    UIStyles, StateManager, LoadingState, LoadingContext,
    create_food_info_card, show_cvd_impact_preview, show_nutrition_table,
    show_error_with_suggestions, create_action_button
)

class FoodAnalysisDisplay:
    """Handles the display of food analysis results"""
    
    def __init__(self, state_manager: StateManager):
        self.state_manager = state_manager
    
    def display_results(self, result: Dict[str, Any]) -> None:
        """Main method to display analysis results"""
        # Check for errors first
        if 'error' in result:
            self._display_error(result)
            return
        
        # Check legacy success field
        if not result.get('success', False):
            self._display_error(result)
            return
        
        # Display successful analysis
        self._display_successful_analysis(result)
    
    def _display_error(self, result: Dict[str, Any]) -> None:
        """Display error messages with helpful suggestions"""
        error_message = result.get('error', 'Unknown error')
        
        # Check for specific "no food" error patterns
        no_food_patterns = [
            'no food', 'not a food', 'appears to be a selfie',
            'not food item', 'no food image detected', 'food not detected',
            'not food-related', 'does not contain food'
        ]
        
        error_lower = error_message.lower()
        is_no_food_error = any(pattern in error_lower for pattern in no_food_patterns)
        
        if is_no_food_error:
            st.warning("🚫 No Food Detected")
            st.info("📷 **Please upload a clear image of food for analysis.**")
            st.markdown(f"💡 **Tip:** {error_message}")
            
            suggestions = [
                "Take a clear photo of the food from above",
                "Ensure good lighting for better detection", 
                "Show the entire dish or meal",
                "Avoid selfies or non-food images",
                "Include multiple food items if it's a complete meal"
            ]
            show_error_with_suggestions("", suggestions)
        else:
            show_error_with_suggestions(f"Analysis error: {error_message}")
    
    def _display_successful_analysis(self, result: Dict[str, Any]) -> None:
        """Display successful analysis results"""
        st.success("✅ Analysis completed successfully!")
        
        # Food information card
        food_name = result.get('food_name', 'Food not identified')
        health_score = self._get_health_score(result)
        nutrition = result.get('nutrition', {})
        
        create_food_info_card(food_name, health_score, nutrition)
        
        # CVD impact preview
        cvd_risk_factors = result.get('cvd_risk_factors', [])
        show_cvd_impact_preview(health_score, cvd_risk_factors)
        
        # Nutritional information
        show_nutrition_table(nutrition)
        
        # Risk factors and recommendations
        self._display_risks_and_recommendations(result)
        
        # Action buttons
        self._display_action_buttons(result)
    
    def _get_health_score(self, result: Dict[str, Any]) -> float:
        """Extract and validate health score"""
        health_score = result.get('heart_health_score', 0)
        try:
            return float(health_score)
        except (ValueError, TypeError):
            return 5.0  # Default value if conversion fails
    
    def _display_risks_and_recommendations(self, result: Dict[str, Any]) -> None:
        """Display risk factors and recommendations"""
        col1, col2 = st.columns(2)
        
        with col1:
            cvd_risks = result.get('cvd_risk_factors', [])
            if cvd_risks:
                st.subheader("⚠️ Cardiovascular Risk Factors")
                for risk in cvd_risks:
                    st.warning(f"• {risk}")
        
        with col2:
            recommendations = result.get('recommendations', [])
            if recommendations:
                st.subheader("💡 Recommendations")
                for rec in recommendations:
                    st.info(f"• {rec}")
    
    def _display_action_buttons(self, result: Dict[str, Any]) -> None:
        """Display action buttons with proper loading states"""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if create_action_button(
                "💾 Save Analysis", 
                "save_analysis_btn",
                button_type="success",
                disabled=self.state_manager.is_loading()
            ):
                self._handle_save_analysis(result)
        
        with col2:
            if create_action_button(
                "🔄 Recalculate CVD", 
                "recalc_cvd_btn",
                button_type="primary",
                disabled=self.state_manager.is_loading()
            ):
                self._handle_recalculate_cvd(result)
        
        with col3:
            with st.expander("🔍 View Detailed Analysis"):
                self._display_detailed_analysis(result)
    
    def _handle_save_analysis(self, result: Dict[str, Any]) -> None:
        """Handle saving analysis with proper loading state"""
        with LoadingContext(self.state_manager, LoadingState.SAVING_ANALYSIS):
            # Import here to avoid circular imports
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
            from front.pages.daily_food import save_analysis_to_history, update_cvd_score_with_nutrition

            save_analysis_to_history(result)
            update_cvd_score_with_nutrition(result)

        st.success("✅ Analysis saved and CVD Score updated!")
        st.rerun()

    def _handle_recalculate_cvd(self, result: Dict[str, Any]) -> None:
        """Handle CVD recalculation with proper loading state"""
        with LoadingContext(self.state_manager, LoadingState.CALCULATING_CVD):
            # Import here to avoid circular imports
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
            from front.pages.daily_food import update_cvd_score_with_nutrition

            update_cvd_score_with_nutrition(result)

        st.success("🎯 CVD Score recalculated based on nutritional analysis!")
        st.rerun()
    
    def _display_detailed_analysis(self, result: Dict[str, Any]) -> None:
        """Display detailed analysis in an organized way"""
        try:
            # Basic information
            st.markdown("### 📋 **Analysis Summary**")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**🍽️ Food:** {result.get('food_name', 'N/A')}")
                health_score = self._get_health_score(result)
                st.write(f"**💚 Heart Score:** {health_score}/10")
            
            with col2:
                timestamp = result.get('analysis_timestamp', 'N/A')
                st.write(f"**📅 Analyzed on:** {timestamp[:19] if timestamp != 'N/A' else 'N/A'}")
                cvd_factors = result.get('cvd_risk_factors', [])
                st.write(f"**⚠️ Risk Factors:** {len(cvd_factors)}")
            
            # Detailed nutritional information
            nutrition = result.get('nutrition', {})
            if nutrition:
                st.markdown("### 📊 **Nutritional Details**")
                
                nutrition_data = []
                key_translations = {
                    'calories': 'Calories',
                    'saturated_fat': 'Saturated Fat (g)',
                    'trans_fat': 'Trans Fat (g)',
                    'sodium': 'Sodium (mg)',
                    'fiber': 'Fiber (g)',
                    'sugar': 'Sugar (g)',
                    'protein': 'Protein (g)',
                    'carbohydrates': 'Carbohydrates (g)',
                    'total_fat': 'Total Fat (g)'
                }
                
                for key, value in nutrition.items():
                    translated_key = key_translations.get(key, key.title())
                    nutrition_data.append([translated_key, str(value)])
                
                if nutrition_data:
                    df = pd.DataFrame(nutrition_data, columns=['Nutrient', 'Value'])
                    st.dataframe(df, use_container_width=True, hide_index=True)
            
            # Risk factors
            cvd_factors = result.get('cvd_risk_factors', [])
            if cvd_factors:
                st.markdown("### ⚠️ **CVD Risk Factors Identified**")
                for factor in cvd_factors:
                    st.warning(f"• {factor}")
            
            # Recommendations
            recommendations = result.get('recommendations', [])
            if recommendations:
                st.markdown("### 💡 **Personalized Recommendations**")
                for rec in recommendations:
                    st.info(f"• {rec}")
            
            # Technical data
            st.markdown("### 🔧 **Technical Data**")
            raw_response = result.get('raw_response', 'No data available')
            
            with st.expander("View raw AI response"):
                if isinstance(raw_response, dict):
                    st.json(raw_response)
                elif isinstance(raw_response, str):
                    try:
                        import json
                        parsed_json = json.loads(raw_response)
                        st.json(parsed_json)
                    except json.JSONDecodeError:
                        st.text(raw_response)
                else:
                    st.write(raw_response)
                    
        except Exception as e:
            st.error(f"Error displaying detailed analysis: {str(e)}")
            st.json(result)

class ImageCaptureInterface:
    """Handles image capture and upload interface"""
    
    def __init__(self, state_manager: StateManager):
        self.state_manager = state_manager
    
    def create_capture_interface(self) -> Dict[str, Any]:
        """Create the image capture interface"""
        st.subheader("📷 Capture or Upload a Food Photo")
        
        # Camera settings
        camera_settings = self._create_camera_settings()
        
        # Tabs for different input methods
        tab1, tab2 = st.tabs(["📸 Take Photo", "📁 File Upload"])
        
        result = {"image": None, "source": None, "settings": camera_settings}
        
        with tab1:
            st.markdown("### Take a photo of your food")
            if camera_settings["camera"] != "Default Camera":
                st.info(f"📷 Using: {camera_settings['camera']}")
            
            camera_photo = st.camera_input(
                "Click to take a photo", 
                key="camera_photo_daily",
                help="Position your food clearly in the frame for best analysis results"
            )
            
            if camera_photo is not None:
                result["image"] = camera_photo
                result["source"] = "camera"
        
        with tab2:
            st.markdown("### Upload a photo from your device")
            uploaded_file = st.file_uploader(
                "Choose an image",
                type=['jpg', 'jpeg', 'png', 'webp', 'gif'],
                help="Supported formats: JPG, JPEG, PNG, WEBP, GIF"
            )
            
            if uploaded_file is not None:
                result["image"] = uploaded_file
                result["source"] = "upload"
        
        return result
    
    def _create_camera_settings(self) -> Dict[str, Any]:
        """Create camera settings interface"""
        with st.expander("📸 Camera Settings"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                camera_options = ["Default Camera", "Front Camera", "Back Camera"]
                selected_camera = st.selectbox(
                    "Camera",
                    camera_options,
                    help="Choose which camera to use"
                )
            
            with col2:
                flash_enabled = st.checkbox("🔦 Flash", value=False)
            
            with col3:
                high_quality = st.checkbox("🎯 HD Quality", value=True)
        
        return {
            "camera": selected_camera,
            "flash": flash_enabled,
            "quality": "high" if high_quality else "standard"
        }
