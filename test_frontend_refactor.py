#!/usr/bin/env python3
"""
Test script to validate the frontend refactor functionality
Tests the key components and state management improvements
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# Add the project root to the path
sys.path.append(os.path.dirname(__file__))

def test_ui_components_import():
    """Test that UI components can be imported successfully"""
    try:
        from front.components.ui_components import (
            StateManager, LoadingState, LoadingContext, UIStyles
        )
        print("✅ UI Components imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import UI components: {e}")
        return False

def test_food_analysis_components_import():
    """Test that food analysis components can be imported successfully"""
    try:
        from front.components.food_analysis_components import (
            FoodAnalysisDisplay, ImageCaptureInterface
        )
        print("✅ Food Analysis Components imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import food analysis components: {e}")
        return False

def test_ui_theme_import():
    """Test that UI theme can be imported successfully"""
    try:
        from front.components.ui_theme import (
            apply_theme, create_header, CVDTheme
        )
        print("✅ UI Theme imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import UI theme: {e}")
        return False

def test_state_manager():
    """Test StateManager functionality"""
    try:
        from front.components.ui_components import StateManager, LoadingState
        
        # Create a mock session state
        mock_session_state = {}
        
        with patch('streamlit.session_state', mock_session_state):
            # Test StateManager initialization
            state_manager = StateManager("test_state")
            
            # Test setting loading state
            state_manager.set_loading_state(LoadingState.ANALYZING_IMAGE)
            
            # Test getting loading state
            current_state = state_manager.get_loading_state()
            assert current_state == LoadingState.ANALYZING_IMAGE
            
            # Test is_loading
            assert state_manager.is_loading() == True
            
            # Test clearing loading
            state_manager.clear_loading()
            assert state_manager.get_loading_state() == LoadingState.IDLE
            assert state_manager.is_loading() == False
            
        print("✅ StateManager functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ StateManager test failed: {e}")
        return False

def test_loading_messages():
    """Test LoadingMessages functionality"""
    try:
        from front.components.ui_components import LoadingMessages, LoadingState
        
        # Test message retrieval
        analyzing_msg = LoadingMessages.get_message(LoadingState.ANALYZING_IMAGE)
        assert "Analyzing the food" in analyzing_msg
        
        saving_msg = LoadingMessages.get_message(LoadingState.SAVING_ANALYSIS)
        assert "Saving analysis" in saving_msg
        
        cvd_msg = LoadingMessages.get_message(LoadingState.CALCULATING_CVD)
        assert "Calculating CVD score" in cvd_msg
        
        print("✅ LoadingMessages functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ LoadingMessages test failed: {e}")
        return False

def test_cvd_theme():
    """Test CVDTheme functionality"""
    try:
        from front.components.ui_theme import CVDTheme
        
        # Test color palette
        assert 'primary' in CVDTheme.COLORS
        assert 'secondary' in CVDTheme.COLORS
        
        # Test global styles generation
        styles = CVDTheme.get_global_styles()
        assert isinstance(styles, str)
        assert 'cvd-card' in styles
        
        print("✅ CVDTheme functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ CVDTheme test failed: {e}")
        return False

def test_food_analysis_display():
    """Test FoodAnalysisDisplay functionality"""
    try:
        from front.components.food_analysis_components import FoodAnalysisDisplay
        from front.components.ui_components import StateManager
        
        # Create mock session state
        mock_session_state = {}
        
        with patch('streamlit.session_state', mock_session_state):
            state_manager = StateManager("test_state")
            display = FoodAnalysisDisplay(state_manager)
            
            # Test health score extraction
            result = {"heart_health_score": "7.5"}
            score = display._get_health_score(result)
            assert score == 7.5
            
            # Test with invalid score
            result = {"heart_health_score": "invalid"}
            score = display._get_health_score(result)
            assert score == 5.0  # Default value
            
        print("✅ FoodAnalysisDisplay functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ FoodAnalysisDisplay test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🧪 Running Frontend Refactor Tests...")
    print("=" * 50)
    
    tests = [
        test_ui_components_import,
        test_food_analysis_components_import,
        test_ui_theme_import,
        test_state_manager,
        test_loading_messages,
        test_cvd_theme,
        test_food_analysis_display
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Frontend refactor is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
